import 'package:flutter/material.dart';
import 'webview_wrapper.dart';
import 'js_bridge.dart';

/// WebView弹窗组件
class WebViewDialog extends StatefulWidget {
  final String url;
  final String title;
  final bool showToolBar;
  final VoidCallback? onClose;
  final JSBridge? jsBridge;

  const WebViewDialog({
    super.key,
    required this.url,
    required this.title,
    this.showToolBar = false,
    this.onClose,
    this.jsBridge,
  });

  @override
  State<WebViewDialog> createState() => _WebViewDialogState();
}

class _WebViewDialogState extends State<WebViewDialog> {
  late WebViewWrapperController _webViewController;
  double _statusBarHeight = 0;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewWrapperController();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    _statusBarHeight = mediaQuery.padding.top;

    return Material(
      color: Colors.white,
      child: Stack(
        children: [
          Column(
            children: [
              // 状态栏占位
              SizedBox(height: _statusBarHeight),
              // WebView占据剩余空间
              Expanded(
                child: WebViewWrapper(
                  controller: _webViewController,
                  initialUrl: widget.url,
                  jsBridge: widget.jsBridge ?? JSBridge(context, webViewController: _webViewController),
                  backgroundColor: Colors.transparent,
                  onPageFinished: () {},
                ),
              ),
              // 如果需要显示工具栏，则添加工具栏
              if (widget.showToolBar) _buildToolBar(),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建工具栏
  Widget _buildToolBar() {
    return Container(
      height: 49.0,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.5),
        border: Border(top: BorderSide(color: Colors.grey[200]!, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: const Icon(Icons.close),
            iconSize: 14.0,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 30.0, minHeight: 20.0),
            onPressed: () {
              if (widget.onClose != null) {
                widget.onClose!();
              }
              Navigator.of(context).pop();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            iconSize: 14.0,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 30.0, minHeight: 20.0),
            onPressed: () {
              _webViewController.reload();
            },
          ),
        ],
      ),
    );
  }
}