import 'dart:convert';
import 'dart:io';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/address_list.dart';
import 'package:dlyz_flutter/model/function_area.dart';
import 'package:dlyz_flutter/pages/community/community_page.dart';
import 'package:dlyz_flutter/pay/pay_manager.dart';
import 'package:dlyz_flutter/providers/function_area_provider.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/webview/web_router.dart';
import 'package:dlyz_flutter/webview/webview_wrapper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import '../pages/bind/switch_bind_character_page.dart';
import '../pages/community/switch_bind_role.dart';
import '../pages/settings/AddressManagementPage.dart';
import '../providers/user_provider.dart';
import '../utils/permission_utils.dart';
import '../net/api/address_service.dart';

/// JavaScript消息结构
class JSMessage {
  final String method;
  final Map<String, dynamic> data;

  JSMessage({
    required this.method,
    required this.data,
  });

  factory JSMessage.fromJson(Map<String, dynamic> json) {
    return JSMessage(
      method: json['method'] ?? '',
      data: json['data'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'data': data,
    };
  }
}

/// JavaScript通信桥接器
class JSBridge {
  /// 消息处理器映射
  final Map<String, Function(JSMessage)> _handlers = {};
  
  /// 通用消息监听器
  Function(JSMessage)? onMessage;

  final BuildContext _context;

  WebViewWrapperController? _webViewController;

  JSBridge(this._context, {WebViewWrapperController? webViewController})
      : _webViewController = webViewController {
    _registerDefaultHandlers();
  }

  /// 注册默认处理器
  void _registerDefaultHandlers() {
    // 跳转外部浏览器
    registerHandler('openActionBrowser', (message) {
      String url = message.data["url"] ?? '';
      if (url.isNotEmpty) {
        ChannelManager().openUrl(url: url);
      }
    });

    // Toast提示
    registerHandler('showToast', (message) {
      Fluttertoast.showToast(
        msg: message.data["message"] ?? '',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    });

    // 关闭页面
    registerHandler('close', (message) {
      Navigator.pop(_context);
    });

    //查询权限是否授权
    registerHandler('isHasPermission', (message) async {
      String permission = message.data["permissionName"] ?? '';
      PermissionType? permissionType;
      if ('notification' == permission) {
        permissionType = PermissionType.notification;
      }
      if (permissionType == null) return;
      final isHasPermission = await PermissionUtils.isPermissionGranted(permissionType);
      String callback = message.data["callback"] ?? '';
      LogUtil.d("isHasPermission: $isHasPermission, callback = $callback");
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, {
          'isHasPermission': isHasPermission
        });
      }
    });

    //申请权限
    registerHandler('requestPermission', (message) async {
      String permission = message.data["permissionName"] ?? '';
      PermissionType? permissionType;
      if ('notification' == permission) {
        permissionType = PermissionType.notification;
      }
      if (permissionType == null) return;
      PermissionUtils.requestPermission(_context, permissionType).then((isGranted) {
        String callback = message.data["callback"] ?? '';
        LogUtil.d("requestPermission: $isGranted, callback = $callback");
        if (callback.isNotEmpty) {
          _executeJsMethod(callback, {
            'isGranted': isGranted
          });
        }
      });
    });

    //拉起角色授权弹窗
    registerHandler('showGameBindingDialog', (message) async {
      String callback = message.data["callback"] ?? '';
      SwitchBindRoleDialog.showCharacterSwitchDialog(
        _context,
        onCharacterSwitched: () {

        },
        onNavigateToBindCharacter: () {

        },
        onClose: () {
          if (callback.isNotEmpty) {
            final selectRole = SwitchBindRoleDialog.getSelectedRole(_context);
            _executeJsMethod(callback, {
              'selectRole': selectRole
            });
          }
        }
      );
    });

    //获取绑定角色列表
    registerHandler('getBindingRoleData', (message) async {
      final roles = SwitchBindRoleDialog.getBindingRoles(_context);
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, {
          'roles': roles
        });
      }
    });

    //跳转游戏绑定页
    registerHandler('jumpGameBindingPage', (message) async {
      var result = await Navigator.push(
        _context,
        MaterialPageRoute(
          builder: (context) => const SwitchBindCharacterPage(),
        ),
      );
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        final selectRole = SwitchBindRoleDialog.getSelectedRole(_context);
        _executeJsMethod(callback, {
          'selectRole': selectRole
        });
      }
    });

    //打开webview
    registerHandler('openWebview', (message) async {
      String url = message.data["url"] ?? '';
      final ticket = _context.read<UserProvider>().currentTicket;
      Map<String, dynamic> params = {
        'app_ticket': ticket
      };
      if (url.isNotEmpty) {
        WebRouter.jumpToWebPage(_context, url, params: params);
      }
    });

    //支付
    registerHandler('pay', (message) async {
      Map<String, String> result = await PayManager.getInstance().pay(message.data);
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, result);
      }
    });

    //跳转特惠商城，android跳转圈子，ios跳转特惠商城
    registerHandler('jumpSpecialStore', (message) async {
      if (Platform.isAndroid) {
        Navigator.push(
          _context,
          MaterialPageRoute(
            builder: (context) => const CommunityPage(),
          ),
        );
      } else {
        FunctionArea? area = Provider
            .of<FunctionAreaProvider>(_context, listen: false)
            .findByName(FunctionModuleType.direct_pay.name);
        LogUtil.d('FunctionModuleType.direct_pay.name = ${FunctionModuleType.direct_pay.name}');
        if (area != null) {
          LogUtil.d('area.areaEntrance = ${area.areaEntrance}');
          WebRouter.jumpToWebPage(_context, area.areaEntrance);
        } else {
          LogUtil.d('area is null');
        }
      }
    });

    // 新增地址页面
    registerHandler('openAddAddress', (message) async {
      String callback = message.data["callback"] ?? '';
      
      var result = await Navigator.push(
        _context,
        MaterialPageRoute(
          builder: (context) => const AddAddressPage(),
        ),
      );
      
      if (callback.isNotEmpty) {
        if (result != null && result is Map<String, dynamic>) {
          _executeJsMethod(callback, result);
        } else {
          _executeJsMethod(callback, {
            'success': false,
            'response': {
              'code': 400,
              'message': '用户退出页面',
              'data': null
            }
          });
        }
      }
    });

    // 修改地址页面
    registerHandler('openEditAddress', (message) async {
      String callback = message.data["callback"] ?? '';
      
      // 从消息中提取地址数据并封装为addressData
      Map<String, dynamic> addressData = {
        'address_id': message.data["address_id"] ?? -1,
        'contact_name': message.data["contact_name"] ?? '',
        'province': message.data["province"] ?? '',
        'phone': message.data["phone"] ?? '',
        'phone_country_code': message.data["phone_country_code"] ?? '+86',
        'district': message.data["district"] ?? '',
        'detail_address': message.data["detail_address"] ?? '',
        'city': message.data["city"] ?? '',
        'is_default_address': message.data["is_default_address"] ?? '',
      };

      // 检查必要的地址数据
      if (addressData['address_id'] == -1 || addressData['contact_name'] == '' ||
          addressData['province'] == '' || addressData['phone'] == '' ||
          addressData['phone_country_code'] == '' || addressData['district'] ==
          '' || addressData['detail_address'] == '' || addressData['city'] == ''
      ) {
        if (callback.isNotEmpty) {
          _executeJsMethod(callback, {
            'success': false,
            'response': {
              'code': 400,
              'message': '地址不完整',
              'data': null
            }
          });
        }
        return;
      }
      
      try {
        // 构造地址对象
        final address = AddressList.fromJson(addressData);
        
        // 获取默认地址，判断当前地址是否为默认地址
        bool isDefaultAddress = false;
        if (addressData['is_default_address'] == 'true'){
          isDefaultAddress = true;
        }else if(addressData['is_default_address'] == ''){
          try {
            final defaultResponse = await AddressService.getDefaultAddress(context: _context);
            if (defaultResponse.success &&
                defaultResponse.data != null &&
                defaultResponse.data!.is_exist &&
                defaultResponse.data!.address != null) {
              isDefaultAddress = defaultResponse.data!.address!.address_id == address.address_id;
            }
          } catch (e) {
            LogUtil.e('获取默认地址失败: $e');
            // 默认地址获取失败不影响地址编辑功能，继续执行
          }
        }
        
        var result = await Navigator.push(
          _context,
          MaterialPageRoute(
            builder: (context) => EditAddressPage(
              address: address,
              isDefaultAddress: isDefaultAddress,
            ),
          ),
        );
        
        if (callback.isNotEmpty) {
          if (result != null && result is Map<String, dynamic>) {
            _executeJsMethod(callback, result);
          } else {
            _executeJsMethod(callback, {
              'success': false,
              'response': {
                'code': 400,
                'message': '用户退出页面',
                'data': null
              }
            });
          }
        }
      } catch (e) {
        LogUtil.e('解析地址数据失败: $e');
        if (callback.isNotEmpty) {
          _executeJsMethod(callback, {
            'success': false,
            'response': {
              'code': 400,
              'message': '解析地址数据异常',
              'data': null
            }
          });
        }
      }
    });
  }

  void _executeJsMethod(String methodName, Map<String, dynamic> params) {
    if (_webViewController == null) return;
    _webViewController!.executeJavaScript("window.$methodName('${jsonEncode(params)}')");
  }

  /// 注册消息处理器
  void registerHandler(String method, Function(JSMessage) handler) {
    _handlers[method] = handler;
  }

  /// 移除消息处理器
  void unregisterHandler(String method) {
    _handlers.remove(method);
  }

  /// 处理来自WebView的消息
  void handleMessage(String message) {
    try {
      final Map<String, dynamic> messageMap = jsonDecode(message);
      final jsMessage = JSMessage.fromJson(messageMap);
      
      // 首先尝试特定的处理器
      if (onMessage != null) {
        onMessage!(jsMessage);
        return;
      }

      // 通用的处理器
      final handler = _handlers[jsMessage.method];
      if (handler != null) {
        handler(jsMessage);
        return;
      }
    } catch (e) {
      debugPrint('解析JavaScript消息失败: $e');
      debugPrint('原始消息: $message');
    }
  }
}