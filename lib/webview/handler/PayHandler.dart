import 'package:dlyz_flutter/webview/js_bridge.dart';

import '../../pay/pay_manager.dart';
import '../webview_bridge_interface.dart';

class PayHandler extends IWebViewBridge {

  static final String BRIDGE_NAME = "pay";

  PayHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    Map<String, String> result = await PayManager.getInstance().pay(message.data);
    String callback = message.data["callback"] ?? '';
    if (callback.isNotEmpty) {
      executeJsMethod(callback, result);
    }
  }

}