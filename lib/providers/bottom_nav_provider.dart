
import 'package:flutter/material.dart';
import 'dart:math';

class BottomNavProvider extends ChangeNotifier {
  int _currentIndex = 0;
  int _previousIndex = 0;
  int _lastGameTabTapTime = 0; // 记录上次点击游戏Tab的时间
  static const int _doubleTapTimeout = 300; // 双击超时时间（毫秒）
  static const int _communityTabIndex = 0; // 圈子Tab的索引
  static const int _gameTabIndex = 1; // 游戏Tab的索引

  int get currentIndex => _currentIndex;
  int get previousIndex => _previousIndex;

  void updateIndex(int index) {
    _previousIndex = _currentIndex;
    _currentIndex = index;
    notifyListeners();
  }

  /// 判断是否切换到圈子页面
  bool isChangeToCommunityPage() {
    return _currentIndex == _communityTabIndex && _previousIndex != _communityTabIndex;
  }

  /// 判断是否切换到游戏页面
  bool isChangeToGamesPage() {
    return _currentIndex == _gameTabIndex && _previousIndex != _gameTabIndex;
  }

  /// 判断是否单击圈子页面
  bool isTapCommunityPage() {
    return _currentIndex == _communityTabIndex && _previousIndex == _communityTabIndex;
  }

  /// 判断是否双击了游戏Tab图标
  bool isDoubleTapGameTab() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final isGameTab = _currentIndex == _gameTabIndex;
    
    if (isGameTab && (now - _lastGameTabTapTime) < _doubleTapTimeout) {
      // 双击事件触发，重置时间戳
      _lastGameTabTapTime = 0;
      return true;
    } else if (isGameTab) {
      // 单击事件，记录时间戳
      _lastGameTabTapTime = now;
      return false;
    }
    
    // 点击了其他Tab，重置时间戳
    _lastGameTabTapTime = 0;
    return false;
  }
}