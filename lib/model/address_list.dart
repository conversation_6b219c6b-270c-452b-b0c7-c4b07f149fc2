class AddressList {
  final int address_id;
  final String contact_name;
  final String province;
  final String phone;
  final String phone_country_code;
  final String district;
  final String detail_address;

  AddressList({
    required this.address_id,
    required this.contact_name,
    required this.province,
    required this.phone,
    required this.phone_country_code,
    required this.district,
    required this.detail_address,
  });

  factory AddressList.fromJson(Map<String, dynamic> json) {
    return AddressList(
      address_id: json['address_id'] is int 
          ? json['address_id'] 
          : int.tryParse(json['address_id']?.toString() ?? '0') ?? 0,
      contact_name: json['contact_name']?.toString() ?? '',
      province: json['province']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      phone_country_code: json['phone_country_code']?.toString() ?? '',
      district: json['district']?.toString() ?? '',
      detail_address: json['detail_address']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address_id': address_id,
      'contact_name': contact_name,
      'province': province,
      'phone': phone,
      'phone_country_code': phone_country_code,
      'district': district,
      'detail_address': detail_address,
    };
  }

  @override
  String toString() {
    return 'AddressList{address_id: $address_id, contact_name: $contact_name, province: $province, phone: $phone, phone_country_code: $phone_country_code, district: $district, detail_address: $detail_address}';
  }
}

class AddressListResponse {
  final List<AddressList> list;
  final int total;

  AddressListResponse({
    required this.list,
    required this.total,
  });

  factory AddressListResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> addressList = json['list'] ?? json['data'] ?? [];
    final addresses = addressList.map((item) => AddressList.fromJson(item as Map<String, dynamic>)).toList();
    
    return AddressListResponse(
      list: addresses,
      total: json['total'] is int 
          ? json['total'] 
          : int.tryParse(json['total']?.toString() ?? '0') ?? addresses.length,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list.map((address) => address.toJson()).toList(),
      'total': total,
    };
  }

  @override
  String toString() {
    return 'AddressListResponse{list: $list, total: $total}';
  }
}

class DefaultAddressResponse {
  final bool is_exist;
  final AddressList? address;

  DefaultAddressResponse({
    required this.is_exist,
    this.address,
  });

  factory DefaultAddressResponse.fromJson(Map<String, dynamic> json) {
    return DefaultAddressResponse(
      is_exist: json['is_exist'] ?? false,
      address: json['is_exist'] == true && json.containsKey('address_id')
          ? AddressList.fromJson(json)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_exist': is_exist,
      if (address != null) ...address!.toJson(),
    };
  }

  @override
  String toString() {
    return 'DefaultAddressResponse{is_exist: $is_exist, address: $address}';
  }
}