import 'dart:convert';

import 'package:dlyz_flutter/components/common_alert.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dlyz_flutter/manager/network_manager.dart';
import 'package:dlyz_flutter/pay/pay_type.dart';
import 'package:dlyz_flutter/pages/demo/privacy_demo.dart';
import 'package:dlyz_flutter/pages/privacy/privacy_manager.dart';
import 'package:dlyz_flutter/services/app_route_manager.dart';
import 'package:dlyz_flutter/pages/download/download_test.dart';
import 'package:dlyz_flutter/providers/user_provider.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../manager/channel_manager.dart';
import '../../model/connectivity_result.dart';
import '../../model/game_binding_info.dart';
import '../../utils/md5_utils.dart';
import '../../utils/device_info_util.dart';
import '../../utils/permission_utils.dart';
import '../../utils/sp_utils.dart';
import '../../webview/web_router.dart';
import '../../webview/webview_page.dart';
import '../../webview/webview_dialog.dart';
import '../update/update_dialog.dart';
import 'gateway_encrypt_demo.dart';
import 'guide_demo_page.dart';
import 'bottom_sheet_demo_page.dart';
import 'proxy_config_page.dart';
import 'dart:io';
class DemoPage extends StatefulWidget {
  const DemoPage({super.key, required this.title});

  final String title;

  @override
  State<DemoPage> createState() => _DemoPageState();
}

class _DemoPageState extends State<DemoPage> {
  final String _counter = "下载进度：0%";

  // 游戏包名输入控制器
  final TextEditingController _packageNameController = TextEditingController(
    text: Platform.isIOS ? "com.37ios.test" : "com.sy.yxjun"
  );

  final TextEditingController _urlController = TextEditingController(
      text: 'https://user.37.com.cn/douluo-universe/gift'
  );

  // WebView URL输入控制器
  final TextEditingController _webViewUrlController = TextEditingController(
    text: 'https://www.baidu.com'
  );

  // 是否显示WebView输入框
  bool _showWebViewInput = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const WebViewPage(title: 'WebView Demo'),
                      ),
                    );
                  },
                  child: const Text('打开WebView演示'),
                ),
                ...commonDemoLayout(),
                ...spDemoLayout(),
                ...md5DemoLayout(),
                ...devDemoLayout(),
                ...idfaDemoLayout(),
                ...oaidDemoLayout(),
                ...gatewayEncryptDemoLayout(),
                ...downLoadApkDemoLayout(),
                ...downloadTestLayout(),
                ...errorDemoLayout(),
                ...permissionDemoLayout(),
                ...appListsDemoLayout(),
                ...guideDemoLayout(),
                ...bottomSheetDemoLayout(),
                ...proxyConfigDemoLayout(),
                ...privacyDemoLayout(),
                ...privacyClearDemoLayout(),
                ...gameBindingDemoLayout(),
                ...webViewDemoLayout(),
                ...payDemoLayout(),
                ...openWebViewDemoLayout(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///sp demo
  List spDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          SpManager.getInstance().put("counter", _counter);
        },
        child: const Text('存储sp'),
      ),
      ElevatedButton(
        onPressed: () {
          var toastMsg = SpManager.getInstance().getString("counter") ?? "null";
          Fluttertoast.showToast(
            msg: toastMsg,
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('获取sp'),
      ),
    ];
  }


  ///md5 demo
  List md5DemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          var md5 = Md5Utils.generateMd5(_counter);
          Fluttertoast.showToast(
            msg: md5,
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('MD5加密'),
      ),
    ];
  }

  /// dev 生成与展示 demo
  List devDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          final dev = await DeviceInfoUtil.getDev();
          CommonAlert.show(
            context: context,
            title: "dev",
            content: dev,
          );
        },
        child: const Text('生成并显示dev'),
      ),
    ];
  }

  /// IDFA 获取与展示 demo
  List idfaDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          final idfa = await DeviceInfoUtil.getIDFA();
          CommonAlert.show(
            context: context,
            title: "IDFA",
            content: idfa ?? "获取失败",
          );
        },
        child: const Text('获取并显示IDFA'),
      ),
      ElevatedButton(
        onPressed: () async {
          final status = await PermissionUtils.requestTrackingPermission();
          String statusText = "";
          switch (status) {
            case "authorized":
              statusText = "已授权";
              break;
            case "denied":
              statusText = "已拒绝";
              break;
            case "restricted":
              statusText = "受限制";
              break;
            case "notDetermined":
              statusText = "未确定";
              break;
            case "not_supported":
              statusText = "不支持（非iOS设备）";
              break;
            case "error":
              statusText = "请求失败";
              break;
            default:
              statusText = "未知状态: $status";
          }
          CommonAlert.show(
            context: context,
            title: "广告追踪权限状态",
            content: statusText,
          );
        },
        child: const Text('请求广告追踪权限'),
      ),
    ];
  }

  /// OAID 获取与展示 demo
  List oaidDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          final oaidResult = await ChannelManager().getOAID();
          
          String content = '';
          if (oaidResult['oaid'] != null) {
            content += 'OAID: ${oaidResult['oaid']}\n';
          }
          if (oaidResult['aaid'] != null) {
            content += 'AAID: ${oaidResult['aaid']}\n';
          }
          if (oaidResult['vaid'] != null) {
            content += 'VAID: ${oaidResult['vaid']}\n';
          }
          
          if (content.isEmpty) {
            content = '获取失败或不支持';
          }
          
          CommonAlert.show(
            context: context,
            title: "设备标识符信息",
            content: content.trim(),
          );
        },
        child: const Text('获取OAID/AAID/VAID'),
      ),
    ];
  }

  ///网关加密拦截器demo
  List gatewayEncryptDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const GateWayEncryptDemo(),
            ),
          );
        },
        child: const Text('网关加密拦截器'),
      ),
    ];
  }



  ///下载APk demo
  List downLoadApkDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          VersionUpdateHelper.showVersionUpdateDialog(
                  context,
                  onUpdateNow: () {
                    Navigator.of(context).pop();
                    // 这里可以添加更新逻辑
                    print('用户选择立即更新');
                  },
                  downloadUrl:'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk',
                  message: '发现新版本，优化了性能与稳定性，请立即更新', // 可选；不传则用默认文案
                  version: 'V1.0.1'
                  // exitAppOnClose: false,  // false为关闭弹窗，true为退出应用
                );
        },
        child: const Text('强更'),
      ),

    ];
  }

  ///下载测试demo
  List downloadTestLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const DownloadTest(),
            ),
          );
        },
        child: const Text('下载管理器测试'),
      ),
    ];
  }

  ///异常demo
  List errorDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          throw Exception('测试异常');
        },
        child: const Text('测试异常'),
      ),
    ];
  }

  ///权限获取demo
  List permissionDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestCameraPermission(
            context,
            description: '需要相机权限来拍摄照片和录制视频',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "相机权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相机权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请相机权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestMicrophonePermission(
            context,
            description: '需要麦克风权限来录制音频',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "麦克风权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "麦克风权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请麦克风权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestStoragePermission(
            context,
            description: '需要存储权限来保存文件',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "存储权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "存储权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请存储权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestLocationPermission(
            context,
            description: '需要位置权限来获取您的位置信息',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "位置权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "位置权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请位置权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestPhotosPermission(
            context,
            description: '需要相册权限来保存相片',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "相册权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相册权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请相册权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestNotificationPermission(
            context,
            description: '需要通知权限来推送信息',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "通知权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "通知权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请通知权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          if (await PermissionUtils.isPermissionGranted(PermissionType.camera)) {
            Fluttertoast.showToast(
              msg: "相机权限已获取",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相机权限未获取",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('检查相机权限'),
      ),
    ];
  }

  ///应用列表demo
  List appListsDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          List appLists = await PermissionUtils.getInstalledApps();
          print("applist:"+appLists.toString());
          Fluttertoast.showToast(
            msg: appLists.toString(),
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('获取应用列表'),
      ),
    ];
  }

  ///攻略页面demo
  List guideDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const GuideDemoPage(),
            ),
          );
        },
        child: const Text('游戏攻略页面'),
      ),
    ];
  }

  ///底部弹窗demo
  List bottomSheetDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BottomSheetDemoPage(),
            ),
          );
        },
        child: const Text('底部弹窗演示'),
      ),
    ];
  }

  /// 通用弹窗demo
  List commonDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          CommonAlert.show(context: context, title: "提示", content: "请选择是否授权当前登录信息《斗罗宇宙》", barrierDismissible: false);
        },
        child: const Text('通用提示弹窗'),
      ),
      ElevatedButton(
        onPressed: () async {
          final networkStatus = await NetworkManager().checkNetworkConnectivity();
          Fluttertoast.showToast(
            msg: "$networkStatus",
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('检查当前网络'),
      ),
      ElevatedButton(
        onPressed: () async {
          final result = NetworkManager().getNetworkAccessControl();
          Fluttertoast.showToast(msg: '当前播放设置：${result.name}');
        },
        child: const Text('检查当前播放设置'),
      ),
      ElevatedButton(
        onPressed: () async {
          List<ConnectivityResult> currentConnectivity = await NetworkManager().checkNetworkConnectivity();
          bool isAllowed = NetworkManager().isConnectionAllowed(currentConnectivity);
          Fluttertoast.showToast(msg: '当前播放设置：$isAllowed');
        },
        child: const Text('检查当前网络连接是否允许播放'),
      ),
      ElevatedButton(
        onPressed: () async {
          // 弹出选择对话框
          NetworkAccessControl? selected = await _showNetworkAccessControlDialog(context);
          
          // 如果用户选择了某个选项，则更新网络访问控制状态
          if (selected != null) {
            NetworkManager().setNetworkAccessControl(selected);
            
            // 显示一个提示，告知用户已更新设置
            Fluttertoast.showToast(msg: '网络访问控制已更新为: ${_getNetworkAccessControlDescription(selected)}');
          }
        },
        child: const Text('切换播放设置'),
      ),
    ];
  }

  ///代理配置demo
  List proxyConfigDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ProxyConfigPage(),
            ),
          );
        },
        child: const Text('代理配置'),
      ),
    ];
  }

  ///隐私政策demo
  List privacyDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TestPrivacyPage(),
            ),
          );
        },
        child: const Text('隐私政策测试'),
      ),
    ];
  }

  List gameBindingDemoLayout() {
    return [
      // 包名输入框
      Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '游戏包名:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _packageNameController,
              decoration: const InputDecoration(
                hintText: '请输入游戏包名',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
          ],
        ),
      ),
      // 游戏绑定按钮
      ElevatedButton(
        onPressed: () {
          _launchApp();
        },
        child: const Text('游戏绑定演示'),
      ),
    ];
  }

  /// WebView弹窗演示
  List webViewDemoLayout() {
    return [
      // WebView演示按钮
      ElevatedButton(
        onPressed: () {
          _openWebViewDialog();
        },
        child: Text('显示WebView'),
      ),
    ];
  }

  /// 支付demo展示
  List payDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          final tradeInfo = "alipay_sdk=alipay-sdk-java-4.40.133.ALL&app_id=2021004136690593&biz_content=%7B%22extend_params%22%3A%7B%22royalty_freeze%22%3A%22true%22%2C%22sys_service_provider_id%22%3A%222088441791061045%22%7D%2C%22out_trade_no%22%3A%2220250820190455210883%22%2C%22settle_info%22%3A%7B%22settle_detail_infos%22%3A%5B%7B%22amount%22%3A0.01%2C%22trans_in_type%22%3A%22defaultSettle%22%7D%5D%7D%2C%22sub_merchant%22%3A%7B%22merchant_id%22%3A%222088450898951700%22%7D%2C%22subject%22%3A%22%E6%B8%B8%E6%88%8F%E5%85%85%E5%80%BC%22%2C%22total_amount%22%3A%220.01%22%7D&charset=UTF-8&format=json&method=alipay.trade.app.pay&notify_url=new-trade.51fubei.com%2FalipayYzt%2FpayCallback&sign=pMqqurvbSmPYg%2BOy2kac7LbngXQD7xSp12bHDVlLJmjKcz%2BFDXdKLqZutApExURMWFZKeErENLZl%2BRuIuBp8y4F5x59qk8pG3wV1gTRXriYUNkkyTW6XKMJ9EiFb1YtOzqxBXV5LBj7DOfoXEgVuV8eqinyRefi1LgyrkjwptluxSYq5FJ6h2JsU8%2BQYYOLluNDWdpeqzd8xFDMHs8vh3eK6c%2F6UOfgTBBsEJ%2FNEwi26DQ0gi3%2F8A7loBd%2B04wIdNmu2bMvpXkbD%2F8ENHWFWPrXCWAIME6L6uxKQqgOP8%2FCdYKqwbiL%2BPvmFoFC53ddR8xMU6f%2FjCZ6BFojNLXz3oA%3D%3D&sign_type=RSA2&timestamp=2025-08-20+19%3A04%3A55&version=1.0";
          PayResult? payResult = await ChannelManager().androidPay(
              payType: PayType.alipay, orderId: "123456", tradeInfo: tradeInfo);
          if (payResult != null) {
            Fluttertoast.showToast(
              msg: "code = ${payResult.code} message = ${payResult.message}",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: Text('支付测试'),
      ),
    ];
  }

  List openWebViewDemoLayout() {
    return [
      // url输入框
      Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'url:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                hintText: '请输入url',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
          ],
        ),
      ),
      ElevatedButton(
        onPressed: () {
          String url = _urlController.text.trim();
          WebRouter.jumpToWebPage(context, url);
        },
        child: const Text('跳转webview'),
      ),
    ];
  }

  Future<void> _launchApp() async {
    try {
      // 获取输入的包名
      String packageName = _packageNameController.text.trim();
      if (packageName.isEmpty) {
        return;
      }

      var ticket = context.read<UserProvider>().currentTicket;
      if (ticket?.isNotEmpty == true) {
        print("userTicket $ticket");
        print("packageName $packageName");

        var gameBindingInfo = GameBindingInfo(
          appTicket: ticket ?? '',
          appId: AppConfig.appId,
          appPid: AppConfig.pid,
          appGid: AppConfig.gid,
          traceId: GameBindingInfo.generateTraceId()
        );
        print("params ${jsonEncode(gameBindingInfo.toJson())}");

        // 使用输入的包名启动游戏
        ChannelManager().bindingGame(
          packageName: packageName,
          gameBindingInfo: gameBindingInfo,
          gameExtParams: { 'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'}
        );
      } else {
        print("用户未登录或票据为空，无法启动游戏");
      }
    } catch (e) {
      print("启动游戏失败: $e");
    }
  }

  /// 打开WebView弹窗
  void _openWebViewDialog() {

    showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (context) {
          return WebViewDialog(
            url: "https://imgcs.s98s2.com/aicc/downfile/1755241809000.html",
            title: 'Demo',
            showToolBar: true,
          );
        });
  }

  ///清除隐私协议数据demo
  List privacyClearDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          await _clearPrivacyData();
        },
        child: const Text('清除隐私协议本地数据'),
      ),
    ];
  }

  /// 清除隐私协议本地数据
  Future<void> _clearPrivacyData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('privacy_agreed');
      
      // 也可以使用 PrivacyManager 的方法来设置为未同意状态
      // await PrivacyManager.setPrivacyAgreed(false);
      
      Fluttertoast.showToast(
        msg: "隐私协议本地数据已清除",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.green,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    } catch (e) {
      Fluttertoast.showToast(
        msg: "清除失败: $e",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }
}


  /// 显示网络访问控制选择对话框
  Future<NetworkAccessControl?> _showNetworkAccessControlDialog(BuildContext context) async {
    return showDialog<NetworkAccessControl>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择网络访问控制'),
          content: const Text('请选择网络访问控制状态'),
          actions: <Widget>[
            TextButton(
              child: const Text('允许所有网络'),
              onPressed: () {
                Navigator.of(context).pop(NetworkAccessControl.allowAll);
              },
            ),
            TextButton(
              child: const Text('仅允许Wi-Fi'),
              onPressed: () {
                Navigator.of(context).pop(NetworkAccessControl.wifiOnly);
              },
            ),
            TextButton(
              child: const Text('关闭网络访问'),
              onPressed: () {
                Navigator.of(context).pop(NetworkAccessControl.disabled);
              },
            ),
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// 获取网络访问控制状态的描述文本
  String _getNetworkAccessControlDescription(NetworkAccessControl control) {
    switch (control) {
      case NetworkAccessControl.allowAll:
        return '允许所有网络';
      case NetworkAccessControl.wifiOnly:
        return '仅允许Wi-Fi';
      case NetworkAccessControl.disabled:
        return '关闭网络访问';
    }
  }
