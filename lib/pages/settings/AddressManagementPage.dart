import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:city_pickers/city_pickers.dart';
import '../../net/api/address_service.dart';
import '../../model/address_list.dart';
import '../../utils/log_util.dart';
import '../../net/http_base_response.dart';

/// 地址选择工具类
class AddressSelector {
  /// 显示地址选择器
  static Future<Map<String, String>?> showAddressPicker(BuildContext context) async {
    try {
      Result? result = await CityPickers.showCityPicker(
        context: context,
        theme: ThemeData(
          // 基础色彩配置
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF4285F4),
          
          // AppBar 样式 - 简化配置
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.white,
            elevation: 0,
            centerTitle: true,
            titleTextStyle: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            iconTheme: IconThemeData(
              color: Color(0xFF666666),
              size: 24,
            ),
          ),
          
          // 背景色
          scaffoldBackgroundColor: const Color(0xFFFAFAFA),
          
          // 列表项主题 - 精简配置
          listTileTheme: const ListTileThemeData(
            contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            selectedTileColor: Color(0xFFF0F8FF),
            selectedColor: Color(0xFF4285F4),
            titleTextStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
            dense: false,
          ),
          
          // 按钮主题 - 优化性能
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4285F4),
              foregroundColor: Colors.white,
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              minimumSize: const Size(88, 44),
            ),
          ),
          
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              minimumSize: const Size(72, 40),
            ),
          ),
          
          // 颜色方案
          colorScheme: const ColorScheme.light(
            primary: Color(0xFF4285F4),
            surface: Colors.white,
            onSurface: Color(0xFF333333),
          ),
        ),
        locationCode: "110000",
      );

      if (result != null) {
        return {
          'province': result.provinceName ?? '',
          'city': result.cityName ?? '',
          'district': result.areaName ?? '',
        };
      }
      return null;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('地址选择失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return null;
    }
  }
}

class AddressModel {
  final String id;
  final String name;
  final String phone;
  final String province;
  final String city;
  final String district;
  final String detailAddress;
  final bool isDefault;

  AddressModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailAddress,
    this.isDefault = false,
  });

  String get fullAddress => '$province$city$district';
}

class AddressManagementPage extends StatefulWidget {
  const AddressManagementPage({super.key});

  @override
  State<AddressManagementPage> createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<AddressManagementPage> {
  List<AddressList> addresses = [];
  bool isLoading = false;
  AddressList? defaultAddress;

  /// 格式化手机号：去掉+86前缀，第4-7位用*号代替
  String _formatPhoneNumber(String countryCode, String phone) {
    // 去掉+86前缀
    String formattedPhone = phone;
    
    // 如果手机号长度为11位，对第4-7位进行脱敏
    if (formattedPhone.length == 11) {
      formattedPhone = formattedPhone.substring(0, 3) + 
                     '****' + 
                     formattedPhone.substring(7);
    }
    
    return formattedPhone;
  }

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      // 同时获取地址列表和默认地址
      final futures = await Future.wait([
        AddressService.getAddressList(context: context),
        AddressService.getDefaultAddress(context: context),
      ]);
      
      final addressListResponse = futures[0] as BaseResponse<AddressListResponse>;
      final defaultAddressResponse = futures[1] as BaseResponse<DefaultAddressResponse>;
      
      if (addressListResponse.success && addressListResponse.data != null) {
        // 处理默认地址
        AddressList? defaultAddr;
        if (defaultAddressResponse.success && 
            defaultAddressResponse.data != null && 
            defaultAddressResponse.data!.is_exist &&
            defaultAddressResponse.data!.address != null) {
          defaultAddr = defaultAddressResponse.data!.address;
        }
        
        // 对地址列表进行排序：默认地址置顶
        List<AddressList> sortedAddresses = addressListResponse.data!.list;
        if (defaultAddr != null) {
          // 移除列表中的默认地址（避免重复）
          sortedAddresses = sortedAddresses.where((addr) => addr.address_id != defaultAddr!.address_id).toList();
          // 将默认地址插入到列表开头
          sortedAddresses.insert(0, defaultAddr);
        }
        
        setState(() {
          addresses = sortedAddresses;
          defaultAddress = defaultAddr;
          isLoading = false;
        });
        LogUtil.d('地址列表加载成功: ${addresses.length}条记录, 默认地址: ${defaultAddress?.address_id}');
      } else {
        setState(() {
          addresses = [];
          defaultAddress = null;
          isLoading = false;
        });
        LogUtil.e('地址列表加载失败: ${addressListResponse.message}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('加载地址列表失败: ${addressListResponse.message}')),
          );
        }
      }
    } catch (e) {
      setState(() {
        addresses = [];
        defaultAddress = null;
        isLoading = false;
      });
      LogUtil.e('地址列表请求异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('网络请求失败，请检查网络连接')),
        );
      }
    }
  }

  void _navigateToAddAddress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddAddressPage(),
      ),
    ).then((_) => _loadAddresses());
  }

  void _navigateToEditAddress(AddressList address) {
    final isDefault = defaultAddress?.address_id == address.address_id;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditAddressPage(
          address: address,
          isDefaultAddress: isDefault,
        ),
      ),
    ).then((_) => _loadAddresses()); // 编辑完成后重新加载地址列表
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '地址管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(
              color: Color(0xFFE5E5E5),
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: isLoading 
                  ? _buildLoadingState() 
                  : (addresses.isEmpty ? _buildEmptyState() : _buildAddressList()),
            ),
            
            // 底部按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
              margin: const EdgeInsets.only(top: 20),
              child: ElevatedButton(
                onPressed: _navigateToAddAddress,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4285F4),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '新建收货地址',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        color: Color(0xFF4285F4),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 地址图标
          SizedBox(
            width: 140,
            height: 140,
            child: ClipOval(
              child: Image.asset(
                'assets/images/address_icon.png',
                width: 120,
                height: 120,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.location_on,
                    size: 60,
                    color: Color(0xFF9E9E9E),
                  );
                },
              ),
            ),
          ),
          // 空状态文本
          const Text(
            '暂无收货地址',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF9E9E9E),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        final address = addresses[index];
        final isDefault = defaultAddress?.address_id == address.address_id;
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isDefault ? const Color(0xFF4285F4) : const Color(0xFFE5E5E5),
              width: isDefault ? 2 : 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  address.contact_name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                _formatPhoneNumber(address.phone_country_code, address.phone),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                              if (isDefault) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF4285F4),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Text(
                                    '默认',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${address.province}${address.district}${address.detail_address}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF666666),
                              height: 1.4,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _navigateToEditAddress(address),
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        padding: const EdgeInsets.only(left: 16,top:8,right: 8,bottom: 8),
                        child: Image.asset(
                          'assets/images/address_edit_icon.png',
                          width: 16,
                          height: 16,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.edit_outlined,
                              size: 16,
                              color: Color(0xFF666666),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class AddAddressPage extends StatefulWidget {
  const AddAddressPage({super.key});

  @override
  State<AddAddressPage> createState() => _AddAddressPageState();
}

class _AddAddressPageState extends State<AddAddressPage> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _detailController = TextEditingController();
  final _addressInputController = TextEditingController(); // 新增：地址输入框控制器
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedDistrict = '';

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _detailController.dispose();
    _addressInputController.dispose(); // 新增：释放地址输入框控制器
    super.dispose();
  }

  void _pasteAndParseAddress() async {
    try {
      // 如果输入框为空，则从剪贴板粘贴；如果有内容，则直接识别
      String addressText = _addressInputController.text.trim();
      
      if (addressText.isEmpty) {
        // 从剪贴板获取内容并填充到输入框
        final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
        final clipboardText = clipboardData?.text?.trim() ?? '';
        
        if (clipboardText.isEmpty) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('请在输入框中输入地址信息，或剪贴板为空')),
          );
          return;
        }
        
        // 将剪贴板内容填充到输入框
        _addressInputController.text = clipboardText;
        addressText = clipboardText;
      }
      
      if (!mounted) return;

      final parsedData = _parseAddressText(addressText);
      
      if (parsedData.isNotEmpty) {
        setState(() {
          if (parsedData['name'] != null) {
            _nameController.text = parsedData['name']!;
          }
          if (parsedData['phone'] != null) {
            _phoneController.text = parsedData['phone']!;
          }
          if (parsedData['province'] != null) {
            _selectedProvince = parsedData['province']!;
          }
          if (parsedData['city'] != null) {
            _selectedCity = parsedData['city']!;
          }
          if (parsedData['district'] != null) {
            _selectedDistrict = parsedData['district']!;
          }
          if (parsedData['detailAddress'] != null) {
            _detailController.text = parsedData['detailAddress']!;
          }
        });
        
        // 识别成功后清空输入框
        _addressInputController.clear();
        
        if (!mounted) return;
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('未能识别有效地址信息')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('识别失败')),
      );
    }
  }

  Map<String, String> _parseAddressText(String text) {
    final result = <String, String>{};
    
    // 清理输入文本，统一换行符并移除多余空格
    String cleanText = text.trim();
    cleanText = cleanText.replaceAll(RegExp(r'\r\n|\r|\n'), ' '); // 将换行符替换为空格
    cleanText = cleanText.replaceAll(RegExp(r'\s+'), ' '); // 合并多个空格
    
    // 先尝试识别带标签的格式（姓名:、电话:、地址: 等）
    _parseTaggedFormat(cleanText, result);
    
    // 清理已识别的标签格式内容
    String remainingText = cleanText;
    
    // 移除标签格式的内容
    remainingText = remainingText.replaceAll(RegExp(r'(姓名|联系人|收货人|收件人)[:：]\s*[\u4e00-\u9fa5]{2,10}'), '');
    remainingText = remainingText.replaceAll(RegExp(r'(电话|手机|手机号码?)[:：]\s*1[3-9]\d{9}'), '');
    remainingText = remainingText.replaceAll(RegExp(r'(地址|收货地址|详细地址|所在地区)[:：]\s*'), '');
    
    // 清理多余的前缀和空格
    remainingText = remainingText.replaceAll(RegExp(r'(地址[:：]?\s*|收货地址[:：]?\s*|详细地址[:：]?\s*)'), '');
    remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // 如果标签格式没有识别到手机号，尝试从剩余文本识别
    if (result['phone'] == null) {
      final phoneRegex = RegExp(r'1[3-9]\d{9}');
      final phoneMatch = phoneRegex.firstMatch(remainingText);
      if (phoneMatch != null) {
        result['phone'] = phoneMatch.group(0)!;
        remainingText = remainingText.replaceAll(phoneMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别省份 - 优先识别直辖市和特别行政区
    if (result['province'] == null) {
      final provinceRegex = RegExp(r'(北京市|天津市|上海市|重庆市|香港特别行政区|澳门特别行政区|台湾省|[\u4e00-\u9fa5]{2,8}省)');
      final provinceMatch = provinceRegex.firstMatch(remainingText);
      if (provinceMatch != null) {
        result['province'] = provinceMatch.group(0)!;
        
        // 如果是直辖市，同时设为市
        final province = result['province']!;
        if (['北京市', '天津市', '上海市', '重庆市'].contains(province)) {
          result['city'] = province;
        }
        
        remainingText = remainingText.replaceAll(result['province']!, ' ').trim();
      }
    }
    
    // 识别城市 - 仅在非直辖市情况下识别
    if (result['city'] == null) {
      final cityRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}市');
      final cityMatch = cityRegex.firstMatch(remainingText);
      if (cityMatch != null) {
        result['city'] = cityMatch.group(0)!;
        remainingText = remainingText.replaceAll(cityMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别区县
    if (result['district'] == null) {
      final districtRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}[区县]');
      final districtMatch = districtRegex.firstMatch(remainingText);
      if (districtMatch != null) {
        result['district'] = districtMatch.group(0)!;
        remainingText = remainingText.replaceAll(districtMatch.group(0)!, ' ').trim();
      }
    }
    
    // 如果标签格式没有识别到姓名，尝试从剩余文本识别
    if (result['name'] == null) {
      // 分析原始文本结构，尝试找到姓名
      final phoneRegex = RegExp(r'1[3-9]\d{9}');
      final phoneInOriginal = phoneRegex.firstMatch(cleanText);
      if (phoneInOriginal != null) {
        String beforePhone = cleanText.substring(0, phoneInOriginal.start).trim();
        
        // 从开头部分寻找可能的姓名
        final nameRegex = RegExp(r'[\u4e00-\u9fa5]{2,4}(?=\s|$|[0-9])');
        final nameMatch = nameRegex.firstMatch(beforePhone);
        if (nameMatch != null) {
          final potentialName = nameMatch.group(0)!;
          
          // 检查是否为合理的姓名（不是地名）
          if (_isValidName(potentialName)) {
            result['name'] = potentialName;
            remainingText = remainingText.replaceFirst(potentialName, ' ').trim();
          }
        }
      }
      
      // 如果还没找到姓名，从剩余文本中尝试识别
      if (result['name'] == null && remainingText.isNotEmpty) {
        final parts = remainingText.split(RegExp(r'[\s,，]+'));
        for (final part in parts) {
          final nameRegex = RegExp(r'^[\u4e00-\u9fa5]{2,4}$');
          if (nameRegex.hasMatch(part) && _isValidName(part)) {
            result['name'] = part;
            remainingText = remainingText.replaceFirst(part, ' ').trim();
            break;
          }
        }
      }
    }
    
    // 最后处理详细地址
    remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
    remainingText = remainingText.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
    
    // 进一步清理详细地址中可能残留的无用信息
    if (remainingText.isNotEmpty) {
      // 移除可能的重复地名片段
      for (final key in ['province', 'city', 'district', 'name']) {
        if (result[key] != null) {
          remainingText = remainingText.replaceAll(result[key]!, '');
        }
      }
      
      remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
      remainingText = remainingText.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
      
      if (remainingText.isNotEmpty && remainingText.length > 1) {
        result['detailAddress'] = remainingText;
      }
    }
    
    // 如果从标签格式中已经获得了详细地址，需要进一步清理
    if (result['detailAddress'] != null) {
      String detailAddress = result['detailAddress']!;
      
      // 移除详细地址中的省市区重复信息
      for (final key in ['province', 'city', 'district']) {
        if (result[key] != null) {
          detailAddress = detailAddress.replaceAll(result[key]!, '');
        }
      }
      
      detailAddress = detailAddress.replaceAll(RegExp(r'\s+'), ' ').trim();
      detailAddress = detailAddress.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
      
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        result['detailAddress'] = detailAddress;
      } else {
        result.remove('detailAddress');
      }
    }
    
    return result;
  }

  // 解析带标签的格式（姓名:、电话:、地址: 等）
  void _parseTaggedFormat(String text, Map<String, String> result) {
    // 识别姓名标签
    final nameTagRegex = RegExp(r'(姓名|联系人|收货人|收件人)[:：]\s*([\u4e00-\u9fa5]{2,10})');
    final nameTagMatch = nameTagRegex.firstMatch(text);
    if (nameTagMatch != null) {
      final name = nameTagMatch.group(2)!.trim();
      if (_isValidName(name)) {
        result['name'] = name;
      }
    }
    
    // 识别电话标签
    final phoneTagRegex = RegExp(r'(电话|手机|手机号码?)[:：]\s*(1[3-9]\d{9})');
    final phoneTagMatch = phoneTagRegex.firstMatch(text);
    if (phoneTagMatch != null) {
      result['phone'] = phoneTagMatch.group(2)!;
    }
    
    // 优先识别"所在地区"标签（这通常包含省市区信息）
    final regionTagRegex = RegExp(r'所在地区[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|详细地址)[:：]|$)');
    final regionTagMatch = regionTagRegex.firstMatch(text);
    if (regionTagMatch != null) {
      final regionPart = regionTagMatch.group(1)!.trim();
      _parseAddressPart(regionPart, result);
    }
    
    // 识别详细地址标签
    final detailAddressTagRegex = RegExp(r'详细地址[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|所在地区)[:：]|$)');
    final detailAddressTagMatch = detailAddressTagRegex.firstMatch(text);
    if (detailAddressTagMatch != null) {
      final detailAddress = detailAddressTagMatch.group(1)!.trim();
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        result['detailAddress'] = detailAddress;
      }
    }
    
    // 如果没有"所在地区"标签，尝试识别普通的地址标签
    if (result['province'] == null && result['city'] == null && result['district'] == null) {
      final addressTagRegex = RegExp(r'(地址|收货地址)[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|详细地址)[:：]|$)');
      final addressTagMatch = addressTagRegex.firstMatch(text);
      if (addressTagMatch != null) {
        final addressPart = addressTagMatch.group(2)!.trim();
        _parseAddressPart(addressPart, result);
      }
    }
  }

  // 解析地址部分的省市区
  void _parseAddressPart(String addressText, Map<String, String> result) {
    String cleanAddress = addressText.trim();
    
    // 识别省份
    final provinceRegex = RegExp(r'(北京市|天津市|上海市|重庆市|香港特别行政区|澳门特别行政区|台湾省|[\u4e00-\u9fa5]{2,8}省)');
    final provinceMatch = provinceRegex.firstMatch(cleanAddress);
    if (provinceMatch != null) {
      result['province'] = provinceMatch.group(0)!;
      
      // 如果是直辖市，同时设为市
      final province = result['province']!;
      if (['北京市', '天津市', '上海市', '重庆市'].contains(province)) {
        result['city'] = province;
      }
      
      cleanAddress = cleanAddress.replaceAll(result['province']!, ' ').trim();
    }
    
    // 识别城市（非直辖市）
    if (result['city'] == null) {
      final cityRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}市');
      final cityMatch = cityRegex.firstMatch(cleanAddress);
      if (cityMatch != null) {
        result['city'] = cityMatch.group(0)!;
        cleanAddress = cleanAddress.replaceAll(cityMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别区县
    final districtRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}[区县]');
    final districtMatch = districtRegex.firstMatch(cleanAddress);
    if (districtMatch != null) {
      result['district'] = districtMatch.group(0)!;
      cleanAddress = cleanAddress.replaceAll(districtMatch.group(0)!, ' ').trim();
    }
    
    // 识别街道/镇（如果存在）- 将其包含在详细地址的开头
    final streetRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}(街道|镇|乡|社区)');
    final streetMatch = streetRegex.firstMatch(cleanAddress);
    String streetInfo = '';
    if (streetMatch != null) {
      streetInfo = streetMatch.group(0)!;
      cleanAddress = cleanAddress.replaceAll(streetInfo, ' ').trim();
    }
    
    // 剩余部分作为详细地址，街道信息作为详细地址的前缀
    cleanAddress = cleanAddress.replaceAll(RegExp(r'\s+'), ' ').trim();
    if (cleanAddress.isNotEmpty || streetInfo.isNotEmpty) {
      String detailAddress = '';
      if (streetInfo.isNotEmpty) {
        detailAddress = streetInfo;
        if (cleanAddress.isNotEmpty) {
          detailAddress = '$detailAddress $cleanAddress';
        }
      } else {
        detailAddress = cleanAddress;
      }
      
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        // 如果已经有详细地址（来自"详细地址"标签），将街道信息放在前面
        if (result['detailAddress'] != null) {
          if (streetInfo.isNotEmpty) {
            result['detailAddress'] = '$streetInfo ${result['detailAddress']!}';
          }
        } else {
          result['detailAddress'] = detailAddress;
        }
      }
    }
  }

  // 验证是否为有效姓名
  bool _isValidName(String name) {
    // 排除常见的地名词汇和关键词
    final excludeWords = [
      '东路', '西路', '南路', '北路', '中路', '大道', '大街', '小区', '社区', '村委', '街道', '镇区',
      '开发', '工业', '科技', '商业', '住宅', '花园', '公园', '广场', '中心', '大厦', '大楼', '写字楼',
      '芳园', '天河', '海珠', '越秀', '荔湾', '白云', '黄埔', '番禺', '花都', '南沙', '从化', '增城',
      '三七', '互娱', '公司', '有限', '集团', '企业', '单位', '部门'
    ];
    
    for (final exclude in excludeWords) {
      if (name.contains(exclude) || exclude.contains(name) || 
          name.endsWith('路') || name.endsWith('街') || name.endsWith('巷') ||
          name.endsWith('区') || name.endsWith('县') || name.endsWith('市')) {
        return false;
      }
    }
    
    return true;
  }

  void _selectLocation() async {
    final result = await AddressSelector.showAddressPicker(context);
    if (result != null && mounted) {
      setState(() {
        _selectedProvince = result['province'] ?? '';
        _selectedCity = result['city'] ?? '';
        _selectedDistrict = result['district'] ?? '';
      });
    }
  }

  void _saveAddress() async {
    if (_nameController.text.isEmpty || 
        _phoneController.text.isEmpty ||
        _selectedProvince.isEmpty ||
        _detailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写完整信息')),
      );
      return;
    }

    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF4285F4),
        ),
      ),
    );

    try {
      final response = await AddressService.addNewAddress(
        context: context,
        contact_name: _nameController.text.trim(),
        phone_country_code: '+86',
        phone: _phoneController.text.trim(),
        province: _selectedProvince,
        city: _selectedCity.isEmpty ? _selectedProvince : _selectedCity,
        district: _selectedDistrict.isEmpty ? _selectedProvince : _selectedDistrict,
        detail_address: _detailController.text.trim(),
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      LogUtil.d('新增地址响应: code=${response.code}, success=${response.success}, message=${response.message}');

      if (response.code == 1 || response.success) {
        // 保存成功
        if (mounted) {
          Navigator.pop(context, {
            'success': true,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      } else {
        // 保存失败
        if (mounted) {
          Navigator.pop(context, {
            'success': false,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();
      
      LogUtil.e('保存地址异常: $e');
      if (mounted) {
        Navigator.pop(context, {
          'success': false,
          'response': {
            'code': 500,
            'message': '网络请求失败，请检查网络连接',
            'data': null
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 防止键盘弹出时调整布局
      appBar: AppBar(
        title: const Text(
          '新建地址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInputField('联系人', '请填写收货人姓名', _nameController),
                  _buildPhoneField(),
                  _buildLocationField(),
                  const SizedBox(height: 15),
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.symmetric(horizontal: 0),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 整个区域为输入框
                        TextField(
                          controller: _addressInputController,
                          maxLines: null, // 自适应行数
                          minLines: 3,    // 最少3行
                          decoration: const InputDecoration(
                            hintText: '试试粘贴收件人姓名、联系方式、地址等信息，可快速识别',
                            hintStyle: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF999999),
                              height: 1.4,
                            ),
                            border: InputBorder.none, // 去掉边框，保持原有外观
                            contentPadding: EdgeInsets.zero, // 去掉内边距
                            isDense: true, // 紧凑布局
                          ),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                            height: 1.4,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Align(
                          alignment: Alignment.centerRight,
                          child: GestureDetector(
                            onTap: _pasteAndParseAddress,
                            child: const Text(
                              '粘贴并识别地址',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF4285F4),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
            child: ElevatedButton(
              onPressed: _saveAddress,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: const Text(
                '保存',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField(String label, String hint, TextEditingController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF616161),
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              style: const TextStyle(
                fontSize: 15, // 设置文字大小
                color: Colors.black87, // 设置文字颜色
              ),
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: const TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneField() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF616161),
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              style: const TextStyle(
                fontSize: 15, // 设置文字大小
                color: Colors.black87, // 设置文字颜色
              ),
              decoration: const InputDecoration(
                hintText: '请填写收货人手机号',
                hintStyle: TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          const Text(
            '+86',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF616161),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationField() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '选择地址',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF616161),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: _selectLocation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedProvince.isEmpty
                                ? '请选择所在地区'
                                : '$_selectedProvince$_selectedCity$_selectedDistrict',
                            style: TextStyle(
                              fontSize: 15,
                              color: _selectedProvince.isEmpty
                                  ? const Color(0xFFCCCCCC)
                                  : Colors.black87,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF999999),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '详细地址',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF616161),
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _detailController,
                  style: const TextStyle(
                    fontSize: 15, // 设置文字大小
                    color: Colors.black87, // 设置文字颜色
                  ),
                  decoration: const InputDecoration(
                    hintText: '请填写街道门牌等详细信息',
                    hintStyle: TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class EditAddressPage extends StatefulWidget {
  final AddressList address;
  final bool isDefaultAddress;

  const EditAddressPage({
    super.key, 
    required this.address,
    this.isDefaultAddress = false,
  });

  @override
  State<EditAddressPage> createState() => _EditAddressPageState();
}

class _EditAddressPageState extends State<EditAddressPage> {
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _detailController;
  late String _selectedProvince;
  late String _selectedCity;
  late String _selectedDistrict;
  bool _isDefaultAddress = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.address.contact_name);
    _phoneController = TextEditingController(text: widget.address.phone);
    _detailController = TextEditingController(text: widget.address.detail_address);
    _selectedProvince = widget.address.province;
    _selectedCity = widget.address.province; // 使用province作为city，因为API结构显示city可能为空
    _selectedDistrict = widget.address.district;
    _isDefaultAddress = widget.isDefaultAddress; // 根据传入参数设置初始状态
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _detailController.dispose();
    super.dispose();
  }

  void _selectLocation() async {
    final result = await AddressSelector.showAddressPicker(context);
    if (result != null && mounted) {
      setState(() {
        _selectedProvince = result['province'] ?? '';
        _selectedCity = result['city'] ?? '';
        _selectedDistrict = result['district'] ?? '';
      });
    }
  }

  void _saveAddress() async {
    if (_nameController.text.isEmpty || 
        _phoneController.text.isEmpty ||
        _selectedProvince.isEmpty ||
        _detailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写完整信息')),
      );
      return;
    }

    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF4285F4),
        ),
      ),
    );

    try {
      final response = await AddressService.updateAddress(
        context: context,
        address_id: widget.address.address_id.toString(),
        contact_name: _nameController.text.trim(),
        phone_country_code: widget.address.phone_country_code,
        phone: _phoneController.text.trim(),
        province: _selectedProvince,
        city: _selectedCity.isEmpty ? _selectedProvince : _selectedCity,
        district: _selectedDistrict.isEmpty ? _selectedProvince : _selectedDistrict,
        detail_address: _detailController.text.trim(),
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      LogUtil.d('更新地址响应: code=${response.code}, success=${response.success}, message=${response.message}');

      if (response.success) {
        // 更新成功，如果勾选了默认地址，则设置为默认地址
        if (_isDefaultAddress) {
          try {
            final setDefaultResponse = await AddressService.setDefaultAddress(
              context: context,
              address_id: widget.address.address_id,
            );
            
            LogUtil.d('设置默认地址响应: code=${setDefaultResponse.code}, success=${setDefaultResponse.success}, message=${setDefaultResponse.message}');
            
            if (!setDefaultResponse.success) {
              // 设置默认地址失败，但地址更新成功，显示提示
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('地址更新成功，但设置默认地址失败: ${setDefaultResponse.message}')),
                );
              }
            }
          } catch (e) {
            LogUtil.e('设置默认地址异常: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('地址更新成功，但设置默认地址失败')),
              );
            }
          }
        }
        
        // 更新成功
        if (mounted) {
          Navigator.pop(context, {
            'success': true,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      } else {
        // 更新失败
        if (mounted) {
          Navigator.pop(context, {
            'success': false,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();
      
      LogUtil.e('更新地址异常: $e');
      if (mounted) {
        Navigator.pop(context, {
          'success': false,
          'response': {
            'code': 500,
            'message': '网络请求失败，请检查网络连接',
            'data': null
          }
        });
      }
    }
  }

  void _deleteAddress() async {
    // 显示删除确认弹窗
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const DeleteConfirmDialog();
      },
    );

    // 如果用户取消了删除操作
    if (result != true) return;

    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF4285F4),
        ),
      ),
    );

    try {
      final response = await AddressService.deleteAddress(
        context: context,
        address_id: widget.address.address_id,
      );

      // 安全关闭加载对话框
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      LogUtil.d('删除地址响应: code=${response.code}, success=${response.success}, message=${response.message}');

      if (response.success) {
        // 删除成功
        if (mounted) {
          Navigator.pop(context, {
            'success': true,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      } else {
        // 删除失败
        if (mounted) {
          Navigator.pop(context, {
            'success': false,
            'response': {
              'code': response.code,
              'message': response.message,
              'data': response.data
            }
          });
        }
      }
    } catch (e) {
      // 安全关闭加载对话框
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      
      LogUtil.e('删除地址异常: $e');
      if (mounted) {
        Navigator.pop(context, {
          'success': false,
          'response': {
            'code': 500,
            'message': '网络请求失败，请检查网络连接',
            'data': null
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 防止键盘弹出时调整布局
      appBar: AppBar(
        title: const Text(
          '修改地址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildEditInputField('联系人', _nameController.text, _nameController),
                  _buildEditPhoneField(),
                  _buildEditLocationField(),
                  _buildDefaultAddressField(),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _deleteAddress,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: const BorderSide(color: Color(0xFFE5E5E5)),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '删除',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveAddress,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4285F4),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      '保存',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditInputField(String label, String value, TextEditingController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF616161),
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              style: const TextStyle(
                fontSize: 15, // 设置文字大小
                color: Colors.black87, // 设置文字颜色
              ),
              decoration: InputDecoration(
                hintText: value,
                hintStyle: const TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditPhoneField() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF616161),
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              style: const TextStyle(
                fontSize: 15, // 设置文字大小
                color: Colors.black87, // 设置文字颜色
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          const Text(
            '+86',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF616161),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditLocationField() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '选择地址',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF616161),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: _selectLocation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            '$_selectedProvince$_selectedCity$_selectedDistrict',
                            style: const TextStyle(
                              fontSize: 15,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF999999),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '详细地址',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF616161),
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _detailController,
                  style: const TextStyle(
                    fontSize: 15, // 设置文字大小
                    color: Colors.black87, // 设置文字颜色
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultAddressField() {
    final bool isCurrentlyDefault = widget.isDefaultAddress;
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Checkbox(
                  value: _isDefaultAddress,
                  onChanged: isCurrentlyDefault ? null : (bool? value) {
                    setState(() {
                      _isDefaultAddress = value ?? false;
                    });
                  },
                  activeColor: isCurrentlyDefault ? const Color(0xFFCCCCCC) : const Color(0xFF4285F4),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isCurrentlyDefault ? const Color(0xFFCCCCCC) : const Color(0xFFCCCCCC),
                    width: 1.5,
                  ),
                ),
                Text(
                  '设为默认地址',
                  style: TextStyle(
                    fontSize: 14,
                    color: isCurrentlyDefault ? const Color(0xFFCCCCCC) : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 删除确认弹窗组件
class DeleteConfirmDialog extends StatelessWidget {
  const DeleteConfirmDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
              child: const Text(
                '删除确认',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // 内容
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 20),
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(text: '确定要删除这个地址吗'),
                  ],
                ),
              ),
            ),

            // 底部按钮
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: Row(
                children: [
                  // 取消按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(false);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),

                  // 中间分割线
                  Container(
                    width: 1,
                    height: 48,
                    color: const Color(0xFFEEEEEE),
                  ),

                  // 确认删除按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(true);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: Text(
                        '确认删除',
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}