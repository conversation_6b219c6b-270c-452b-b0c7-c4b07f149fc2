class HttpBaseConfig {
  static const String baseUrl = 'http://gamehub-api.37.com.cn';
  static const String validateBaseUrl = 'http://validate-api.37.com.cn';
  static const String pushHost = "http://app-liefer.37.com.cn";
  //论坛BaseUrl
  static const String forumBaseUrl  = '$baseUrl/api/gamehub-api/v1/forum';
  static const String buglessUrl = "http://bugless.shan-yu-tech.com";
  // m层激活接口基础URL
  static const String mActivateBaseUrl = '$baseUrl/api/gamehub-api/v1/msdk';
  // s层激活接口基础URL
  static const String sActivateBaseUrl = '$baseUrl/api/gamehub-api/v1/ssdk';

  static const String gameBaseUrl = '$baseUrl/api/gamehub-api/v1/';
  //
  // static const String mActivateBaseUrl = 'http://m-api.37.com.cn';
  // static const String sActivateBaseUrl = 'http://s-api.37.com.cn';

  /// 判断是否需要加密
  /// 当baseUrl包含'-secure'时返回true，否则返回false
  static bool get shouldUseEncryption => baseUrl.contains('-secure');
}
