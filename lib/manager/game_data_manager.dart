
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/game_center_pocket_data.dart';
import 'package:dlyz_flutter/model/game_post_list.dart';
import 'package:dlyz_flutter/model/mini_program_info.dart';
import 'package:dlyz_flutter/net/api/game_list_service.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:dlyz_flutter/utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:installed_apps/app_info.dart';
import 'package:installed_apps/installed_apps.dart';

import '../model/download_info.dart';

class GameDataManager extends ChangeNotifier {
  static final GameDataManager _instance = GameDataManager._internal();

  factory GameDataManager() {
    return _instance;
  }
  GameDataManager._internal();

  int offset = 0;
  GameFloatConfig floatConfig = GameFloatConfig(floatMsg: false);

  // 存储已安装应用的列表
  List<AppInfo> _installedPackageInfo = [];
  List<String> _installedPackageNames = [];

  
  // 获取已安装应用列表的缓存
  List<AppInfo> get installedPackageInfo => List.unmodifiable(_installedPackageInfo);
  List<String> get installedPackageNames => List.unmodifiable(_installedPackageNames);

  List<GameListDetail> gameList = [];

  Future<void> requestGameList() async {
    try {
      final gameService = GameListService();
      final response = await gameService.getGameList(offset: offset);
      final data = response.data;
      if (response.isSuccess && data != null) {
        reloadGameList(data.record);
        floatConfig = data.floatConfig;
        reloadInstalledGamesIcon();
        debugPrint('游戏中心列表加载成功');
      } else {
        if (Platform.isAndroid) {
          reloadGameList(GameCenterPocketData().androidGameItems.record);
          floatConfig = GameCenterPocketData().androidGameItems.floatConfig;
        } else if (Platform.isIOS) {
          reloadGameList(GameCenterPocketData().iosGameItems.record);
          floatConfig = GameCenterPocketData().iosGameItems.floatConfig;
        }
        reloadInstalledGamesIcon();
        debugPrint('游戏中心列表加载失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('游戏中心列表加载异常$e');
    } finally {
      notifyListeners();
    }
  }

  /// 根据flagId获取小程序跳转配置并跳转
  Future<void> requestJumpMiniProgram(int flagId, {String extra = ""}) async {
    try {
      final gameService = GameListService();
      final response = await gameService.getJumpMiniProgramConfig(flagId: flagId);
      if (response.isSuccess && response.data != null) {
        var skipType = response.data?.skipType;
        if (skipType != null && skipType != 0) {
          final miniProgramPath = response.data?.miniProgramPath ?? '';
          final schemeUrl = response.data?.schemeUrl ?? '';
          ChannelManager().jumpToMiniProgram(info: MiniProgramInfo(
              skipType: skipType,
              appId: response.data?.appId ?? '',
              miniProgramId: response.data?.miniProgramId ?? '',
              miniProgramPath: miniProgramPath + extra,
              schemeUrl: schemeUrl + extra
          ));
        }
      } else {
        debugPrint('跳转小程序失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('跳转小程序异常$e');
    }
  }

  /// 获取已安装应用列表并存储包名
  Future<void> loadInstalledApps() async {
    try {
      _installedPackageInfo = await InstalledApps.getInstalledApps(true, true);
      _installedPackageNames = _installedPackageInfo.map((app) => app.packageName.trim()).toList();
    } catch (e) {
      debugPrint('获取已安装应用列表失败: $e');
      _installedPackageInfo = [];
      _installedPackageNames = [];
    }
  }

  /// 重载游戏本地Icon
  Future<void> reloadInstalledGamesIcon() async {
    try {
      for (GameListDetail tGame in gameList) {
        for (PackageInfo game in tGame.gameActionConfig.packageInfo) {
          reloadInstalledGameIcon(game);
        }
      }
    } catch (e) {
      debugPrint('重载已安装游戏Icon失败: $e');
    }
  }

  Future<void> reloadInstalledGameIcon(PackageInfo game) async {
    try {
      AppInfo? exitPackage = findLocalPackage(game.packageName);
      game.localIcon = exitPackage?.icon;
    } catch (e) {
      debugPrint('重载已安装游戏Icon失败: $e');
    }
  }

  /// 刷新游戏列表数据
  Future<void> reloadGameList(List<GameListDetail> newGameList) async {
    try {
      if (gameList.isEmpty) {
        // 处理新的下载信息
        List<GameListDetail> list = newGameList;
        for (GameListDetail gameListDetail in list) {
          var gameName = gameListDetail.tGidName;
          var gameUrl = "";
          var officialPackageName = "";
          PackageInfo? officialPackage;
          List<PackageInfo> packageList = gameListDetail.gameActionConfig.packageInfo;
          if (packageList.isEmpty) {
            final pocketGame = findPocketGame(gameListDetail.tGid);
            if (pocketGame != null) {
              packageList = pocketGame.gameActionConfig.packageInfo;
              gameName = pocketGame.downloadInfo.name;
              gameUrl = pocketGame.downloadInfo.url;
              officialPackageName = pocketGame.gameActionConfig.officialPackageName ?? "";
            }
          }
          try {
            officialPackage = packageList.firstWhere((item) => item.packageType.isOfficial());
          } catch (e) {
            officialPackage = null;
          }
          if (officialPackage != null) {
            officialPackageName = officialPackage.packageName;
            gameUrl = officialPackage.downloadUrl;
          } else {
            final pocketGame = findPocketGame(gameListDetail.tGid);
            if (pocketGame != null) {
              gameName = pocketGame.downloadInfo.name;
              gameUrl = pocketGame.downloadInfo.url;
              officialPackageName = pocketGame.gameActionConfig.officialPackageName ?? "";
            }
          }
          gameListDetail.gameActionConfig.officialPackageName = officialPackageName;
          gameListDetail.downloadInfo = DownloadInfo(name: gameName, url: gameUrl);
          loadPackageSize(gameListDetail.downloadInfo);
        }
        gameList = list;
        initGamesDownloadHandlers();
        reloadGamesDownloadTasks();
      } else {
        // 拿回旧的下载信息
        List<GameListDetail> list = newGameList;
        for (GameListDetail gameListDetail in list) {
          final tGid = gameListDetail.tGid;
          GameListDetail? existGameDetail = findExistGameDetail(tGid);
          if (existGameDetail != null) {
            gameListDetail.downloadInfo = existGameDetail.downloadInfo;
          }
        }
        gameList = list;
        reloadGamesDownloadTasks();
      }
    } catch (e) {
      debugPrint('重载游戏列表失败: $e');
    }
  }

  /// 初始化下载处理器
  Future<void> initGamesDownloadHandlers() async {
    try {
      for (final game in gameList) {
        DownloadProvider().initializeDownloadHandlers(game.downloadInfo);
      }
    } catch (e) {
      debugPrint('初始化游戏中心下载处理失败: $e');
    }
  }

  /// 重新加载下载任务
  Future<void> reloadGamesDownloadTasks() async {
    try {
      for (final game in gameList) {
        DownloadProvider().reloadDownloadTasks(game.downloadInfo);
      }
    } catch (e) {
      debugPrint('重新加载游戏中心下载任务失败: $e');
    }
  }

  /// 刷新游戏下载信息
  Future<void> refreshGamesDownloadInfo(int tGid, DownloadInfo downloadInfo) async {
    try {
      gameList.firstWhere((item) => item.tGid == tGid).downloadInfo = downloadInfo;
      DownloadProvider().initializeDownloadHandlers(downloadInfo);
    } catch (e) {
      debugPrint('刷新游戏下载信息失败：$e');
    }
  }

  /// 更新游戏安装状态
  Future<void> checkInstallation(List<PackageInfo> packages) async {
    try {
      for (PackageInfo package in packages) {
        if (package.packageName.isEmpty && !package.packageType.isWeChat()) {
          package.hasInstall = false;
        } else if (package.packageType.isWeChat()) {
          package.hasInstall = true;
        } else {
          package.hasInstall = await ChannelManager().checkGameInstalled(packageName: package.packageName);
        }
      }
    } catch (e) {
      debugPrint("检查游戏是否安装失败: $e");
    }
  }

  /// 查询已安装游戏数量（排除官方包）
  int countInstalledGames(List<PackageInfo> packages) {
    return packages.where((package) => package.hasInstall && !package.packageType.isOfficial()).length;
  }

  /// 查询包体大小
  Future<void> loadPackageSize(DownloadInfo downloadInfo) async {
    final dio = Dio();
    try {
      final response = await dio.head(downloadInfo.url);
      final contentLength = response.headers.value('Content-Length');
      final formattedSize = CommonUtils.formatBytes(contentLength);
      if (formattedSize.isNotEmpty) {
        downloadInfo.size = "($formattedSize)";
      }
    } catch (e) {
      debugPrint('查询包体大小失败：$e');
    }
  }

  /// 查询已存在游戏详情
  GameListDetail? findExistGameDetail(int tGid) {
    try {
      GameListDetail result = gameList.firstWhere((item) => item.tGid == tGid);
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 查询兜底游戏
  GameListDetail? findPocketGame(int tGid) {
    try {
      GameListDetail? result;
      if (Platform.isAndroid) {
        result = GameCenterPocketData().androidGameItems.record.firstWhere((item) => item.tGid == tGid);
      } else if (Platform.isIOS) {
        result = GameCenterPocketData().iosGameItems.record.firstWhere((item) => item.tGid == tGid);
      }
      return result;
    } catch (e) {
      return null;
    }
  }

  AppInfo? findLocalPackage(String packageName) {
    try {
      final apps = _installedPackageInfo;
      AppInfo result = apps.firstWhere((item) => item.packageName.isEqualTo(packageName));
      return result;
    } catch (e) {
      return null;
    }
  }


}