import 'package:dlyz_flutter/net/sign_interceptor.dart';
import 'package:dlyz_flutter/net/config/http_base_config.dart';

import '../net/http_service.dart';
import '../net/http_base_response.dart';
import '../net/transformers/response_transformers.dart';
import '../model/game_role.dart';
import '../model/package_info.dart';
import '../model/sub_user.dart';
import '../model/auth_ret_response.dart';

/// 绑定账号服务类
class BindAccountService {
  static final BindAccountService _instance = BindAccountService._internal();
  factory BindAccountService() => _instance;
  BindAccountService._internal();

  HttpService get _httpService => HttpService.getInstance();
  
  // API接口配置
  static const String _baseUrl = 'http://gamehub-api.37.com.cn';
  static const String _roleListPath = '/api/role-kit/v1/gamehub_role_lister/by_tgame';
  static const String _roleListV2Path = '/api/gamehub-api/v1/role/get_list';
  static const String _rolePickPath = '/api/gamehub-api/v1/role/pick';
  static const String _roleFavoritePath = '/api/gamehub-api/v1/role/favorite';
  static const String _packageInfoPath = '/api/gamehub-api/v1/game-center/get-package-info';
  static const String _subUserListPath = '/api/gamehub-api/v1/sub_user/list';
  static const String _authApplyPath = '/api/gamehub-api/v1/sub_user/auth_apply';
  static const String _authRetPath = '/api/gamehub-api/v1/sub_user/auth_ret';
  
  /// 获取角色列表
  /// 
  /// [tgid] 游戏ID
  /// 返回 [Future<BaseResponse<List<GameRole>>>] 角色列表结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<List<GameRole>>> getRoleList({
    required String tgid,
    required String uid,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'tgid': tgid,
        'uid': uid
      };

      // 发起请求
      return await _httpService.get<List<GameRole>>(
        _roleListPath,
        baseUrl: HttpBaseConfig.baseUrl,
        queryParameters: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data is List) {
            return data.map((item) {
              if (item is Map<String, dynamic>) {
                return GameRole.fromJson(item);
              } else if (item is Map) {
                return GameRole.fromJson(Map<String, dynamic>.from(item));
              } else {
                return const GameRole(
                  drid: '',
                  dsid: '',
                  drname: '',
                  dsname: '',
                  drlevel: '',
                  roleGid: 0,
                  rolePid: 0,
                  lastLogin: '',
                );
              }
            }).toList();
          } else {
            return <GameRole>[];
          }
        },
        parseStrategy: ResponseTransformers.stateData<List<GameRole>>(),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取角色列表V2 (接口二)
  /// 
  /// [tgid] 游戏ID
  /// 返回 [Future<BaseResponse<GameRoleListV2Data>>] 角色列表结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<GameRoleListV2Data>> getRoleListV2({
    required String tgid
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'tgid': tgid
      };

      // 发起请求
      return await _httpService.post<GameRoleListV2Data>(
        _roleListV2Path,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => GameRoleListV2Data.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 选择角色 (接口三)
  /// 
  /// [roleFavoriteId] 角色收藏ID
  /// [uid] 用户ID
  /// [rolePid] 角色项目ID
  /// [roleGid] 角色游戏ID
  /// [drid] 角色ID
  /// [dsid] 服务器ID
  /// [drname] 角色名称
  /// [dsname] 服务器名称
  /// 返回 [Future<BaseResponse<RolePickResponseData>>] 选择角色结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<RolePickResponseData>> pickRole({
    required String roleFavoriteId,
    required String uid,
    required String rolePid,
    required String roleGid,
    required String drid,
    required String dsid,
    required String drname,
    required String dsname,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'role_favorite_id': roleFavoriteId,
        'uid': uid,
        'role_pid': rolePid,
        'role_gid': roleGid,
        'drid': drid,
        'dsid': dsid,
        'drname': drname,
        'dsname': dsname,
      };

      // 发起请求
      return await _httpService.post<RolePickResponseData>(
        _rolePickPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => RolePickResponseData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 角色默认选择 (mingling8接口)
  /// 
  /// [roleFavoriteId] 角色收藏ID
  /// [uid] 用户ID
  /// [rolePid] 角色项目ID
  /// [roleGid] 角色游戏ID
  /// [drid] 角色ID
  /// [dsid] 服务器ID
  /// [drname] 角色名称
  /// [dsname] 服务器名称
  /// 返回 [Future<BaseResponse<dynamic>>] 角色默认选择结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<dynamic>> setDefaultRole({
    required String roleFavoriteId,
    required String uid,
    required String rolePid,
    required String roleGid,
    required String drid,
    required String dsid,
    required String drname,
    required String dsname,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'role_favorite_id': roleFavoriteId,
        'uid': uid,
        'role_pid': rolePid,
        'role_gid': roleGid,
        'drid': drid,
        'dsid': dsid,
        'drname': drname,
        'dsname': dsname,
      };

      // 发起请求
      return await _httpService.post<dynamic>(
        _rolePickPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => data,
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 绑定角色 (角色收藏)
  /// 
  /// [tgid] 顶级游戏ID
  /// [uid] 用户ID
  /// [roleList] 角色列表，格式为JSON字符串
  /// 返回 [Future<BaseResponse<dynamic>>] 绑定结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<dynamic>> bindRoles({
    required String tgid,
    required String uid,
    required String roleList,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'tgid': tgid,
        'uid': uid,
        'role_list': roleList,
      };

      // 发起请求
      return await _httpService.post<dynamic>(
        _roleFavoritePath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => data,
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 根据顶级游戏获取包体信息 (接口四)
  /// 
  /// [pid] 斗罗app pid
  /// [gid] 斗罗app gid  
  /// [tgid] 顶级游戏
  /// [os] 操作系统，android或ios
  /// [packageNameList] 包名列表
  /// 返回 [Future<BaseResponse<PackageInfoResponse>>] 包体信息结果
  /// 
  /// 注意：其他公共参数（sign）由SignInterceptor自动添加
  Future<BaseResponse<PackageInfoResponse>> getPackageInfo({
    required String pid,
    required String gid,
    required String tgid,
    required String os,
    required List<String> packageNameList,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'pid': pid,
        'gid': gid,
        'tgid': tgid,
        'os': os,
        'package_name_list': packageNameList,
      };

      // 发起请求
      return await _httpService.post<PackageInfoResponse>(
        _packageInfoPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.json,
        signType: SignType.v3,
        fromJsonT: (data) => PackageInfoResponse.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取关联的账号列表 (接口五)
  /// 
  /// 返回 [Future<BaseResponse<SubUserListResponse>>] 关联账号列表结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<SubUserListResponse>> getSubUserList() async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {};

      // 发起请求 - 使用直接数据策略，因为我们需要完整的响应对象
      return await _httpService.post<SubUserListResponse>(
        _subUserListPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (responseData) {
          // 直接传入完整的响应数据给SubUserListResponse.fromJson
          if (responseData is Map<String, dynamic>) {
            return SubUserListResponse.fromJson(responseData);
          } else {
            throw Exception('响应数据格式不正确');
          }
        },
        parseStrategy: ResponseTransformers.direct<SubUserListResponse>(),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 子用户授权申请 (接口六)
  /// 
  /// 返回 [Future<BaseResponse<String>>] 授权申请结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<String>> authApply() async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {};
      // 发起请求
      return await _httpService.post<String>(
        _authApplyPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data is Map<String, dynamic>) {
            return data['trace_id']?.toString() ?? '';
          }
          return data?.toString() ?? '';
        },
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 子用户授权结果 (接口七)
  /// 
  /// [traceId] 跟踪ID
  /// 返回 [Future<BaseResponse<AuthRetResponse>>] 授权结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<AuthRetResponse>> authRet({
    required String traceId,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'trace_id': traceId,
      };

      // 发起请求
      return await _httpService.post<AuthRetResponse>(
        _authRetPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (responseData) {
          // 直接传入完整的响应数据给AuthRetResponse.fromJson
          if (responseData is Map<String, dynamic>) {
            return AuthRetResponse.fromJson(responseData);
          } else {
            throw Exception('响应数据格式不正确');
          }
        },
        parseStrategy: ResponseTransformers.direct<AuthRetResponse>(),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }
}